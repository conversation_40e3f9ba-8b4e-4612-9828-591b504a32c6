import { inngest } from '../client'
import { createClient } from '@supabase/supabase-js'
import {
  updateRenderStatus,
  renderWithLambda,
  renderWithCloudRun,
} from '../utils/render-utils'
import { incrementUsage } from '@/lib/usage-utils'

// Top-level supabase client for all helpers
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

export const renderVideo = inngest.createFunction(
  {
    id: 'render-video',
    retries: 1, // Cap retries to only 1 attempt
  },
  { event: 'render-video' },
  async ({ event, step }) => {
    let renderRecord: string | null = null

    try {
      // Step 0: Load project data and reconstruct payload
      const fullPayload = await step.run('load-project-data', async () => {
        const {
          projectId,
          userId,
          organizationId,
          exportResolution,
          duration,
          subtitlePosition,
          compositionHeight,
          compositionWidth,
          durationInFrames,
          exportName,
        } = event.data

        if (projectId) {
          // New format: load project data from database
          console.log('Loading project data from database:', projectId)

          const { data: project, error } = await supabase
            .from('projects')
            .select('*')
            .eq('project_id', projectId)
            .single()

          if (error) {
            throw new Error(`Failed to load project: ${error.message}`)
          }

          if (!project) {
            throw new Error(`Project not found: ${projectId}`)
          }

          console.log('✅ Project data loaded from database')

          // Reconstruct inputProps from project data (no JSON parsing needed!)
          const inputProps = {
            scenes: project.scenes,
            subtitlePosition: subtitlePosition,
            orientation: project.orientation,
            compositionWidth: compositionWidth,
            compositionHeight: compositionHeight,
            selectedMusic: project.music,
            musicVolume: project.music?.volume || 30,
            musicEnabled: project.music?.enabled || false,
            captionStyle: project.captionSettings,
            exportName: exportName,
            exportResolution: exportResolution,
            durationInFrames: durationInFrames,
            speech: project.speech,
            duration: duration,
          }

          return {
            inputProps,
            projectId,
            userId,
            organizationId,
          }
        } else {
          // Legacy format: payload is directly in event.data
          console.log('Using legacy payload format')
          return event.data
        }
      })

      const { inputProps, projectId, userId, organizationId } = fullPayload
      // Step 1: Initialize render record
      renderRecord = await step.run('initialize-render', async () => {
        const renderData = {
          project_id: projectId,
          user_id: userId,
          organization_id: organizationId || null,
          status: 'initializing',
          progress: 0,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          export_name: inputProps.exportName || null,
          export_resolution: inputProps.exportResolution || null,
          render_method: null, // will update after method is chosen
        }
        const { data, error } = await supabase
          .from('render_jobs')
          .insert(renderData)
          .select('id')
          .single()

        if (error) {
          console.error('Error creating render job:', error)
          throw error
        }

        return data.id
      })

      // Step 2: Determine render method based on duration
      const renderMethod = await step.run(
        'determine-render-method',
        async () => {
          const estimatedDuration = inputProps.durationInFrames / 30 // Assuming 30fps
          return estimatedDuration > 1800 ? 'cloudrun' : 'lambda' // 30 minutes
        }
      )

      // Update renderMethod in DB
      const { error: methodError } = await supabase
        .from('render_jobs')
        .update({ render_method: renderMethod })
        .eq('id', renderRecord!)

      if (methodError) {
        console.error('Error updating render method:', methodError)
        throw methodError
      }

      // Step 3: Update status to rendering
      await step.run('update-status-rendering', async () => {
        await updateRenderStatus(renderRecord!, 'rendering', 5)
      })

      // Step 4: Render video
      const result = await step.run('render-video', async () => {
        if (!renderRecord) throw new Error('renderRecord is null')
        if (renderMethod === 'lambda') {
          return await renderWithLambda(
            inputProps,
            renderRecord,
            userId,
            projectId
          )
        } else {
          return await renderWithCloudRun(
            inputProps,
            renderRecord,
            userId,
            projectId
          )
        }
      })

      // Step 5: Update final status
      await step.run('finalize-render', async () => {
        await updateRenderStatus(
          renderRecord!,
          'completed',
          100,
          result.publicUrl
        )
      })

      // Step 6: Increment usage for video export
      await step.run('increment-usage', async () => {
        try {
          // Use organization ID if available, otherwise use user ID
          const referenceId = organizationId || userId
          await incrementUsage(referenceId, 'videoExports', 1)
          console.log('✅ Usage incremented for video export')
        } catch (usageError) {
          console.error(
            '❌ Failed to increment usage for video export:',
            usageError
          )
          // Don't fail the entire operation if usage tracking fails
        }
      })

      return {
        renderId: renderRecord,
        publicUrl: result.publicUrl,
        status: 'completed',
      }
    } catch (error) {
      console.error('Render failed:', error)
      if (typeof renderRecord === 'string') {
        await updateRenderStatus(
          renderRecord,
          'failed',
          0,
          undefined,
          (error as Error).message
        )
      }

      throw error
    }
  }
)
