import { inngest } from '../client'
import { createClient } from '@supabase/supabase-js'
import {
  updateRenderStatus,
  renderW<PERSON><PERSON><PERSON>bda,
  renderWithCloudRun,
} from '../utils/render-utils'
import { incrementUsage } from '@/lib/usage-utils'

// Top-level supabase client for all helpers
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

// Types for transformation
interface ProjectData {
  project_id: string
  user_id: string
  organization_id?: string | null
  project_name: string
  method: string
  created_at: Date | null
  updated_at: Date | null
  cover_color: string | null
  cover_pic: string | null
  orientation: string
  duration: string | null
  summary: string | null
  voice_regenerations: number
  speech: {
    enabled: boolean
    src: string
    name: string
    volume: number
    transcript: {
      captions: Array<{
        start: number
        end: number
        sentence: string
        wordBoundries: Array<{ start: number; end: number; word: string }>
      }>
      status: string
    }
  } | null
  background_video: {
    src: string
    muted: boolean
  } | null
  music: {
    enabled: boolean
    src: string
    volume: number
    duration: number
    name: string
  } | null
  caption_settings: {
    enabled: boolean
    fontFamily: string
    fontSize: number
    fontWeight: string
    fontStyle: string
    textColor: string
    highlightColor: string
    backgroundColor: string
    backgroundOpacity: number
    textAlign: string
    textShadow: boolean
    borderRadius: number
    padding: number
    maxWidth: number
    animation: string
  } | null
  scenes: Array<Record<string, unknown>>
  event_id: string | null
  run_id: string | null
  blog_images: string[] | null
}

interface RenderParams {
  subtitlePosition: { x: number; y: number }
  compositionWidth: number
  compositionHeight: number
  exportName: string
  exportResolution: string
  durationInFrames: number
  duration: number
}

/**
 * Transforms raw project data from Supabase into the input props format
 * expected by the video rendering system
 */
function transformProjectDataToInputProps(
  project: ProjectData,
  params: RenderParams
) {
  const {
    subtitlePosition,
    compositionWidth,
    compositionHeight,
    exportName,
    exportResolution,
    durationInFrames,
    duration,
  } = params

  // Transform music data to selectedMusic format
  const selectedMusic = project.music
    ? {
        id: 'project-music',
        title: project.music.name || 'Project Music',
        genre: 'Background',
        mood: 'Neutral',
        artistName: 'Unknown',
        provider: 'PROJECT',
        licenseId: 'project',
        previewUrl: project.music.src,
        durationMillis: (project.music.duration || 0) * 1000,
      }
    : null

  // Transform caption settings to captionStyle format
  const captionStyle = project.caption_settings
    ? {
        id: 'project-caption-style',
        name: 'Project Caption Style',
        isCustom: true,
        createdAt: new Date().toISOString(),
        fontFamily: project.caption_settings.fontFamily,
        fontSize: project.caption_settings.fontSize,
        fontWeight: project.caption_settings.fontWeight as 'normal' | 'bold',
        fontStyle: project.caption_settings.fontStyle as 'normal' | 'italic',
        textColor: project.caption_settings.textColor,
        highlightColor: project.caption_settings.highlightColor,
        backgroundColor: project.caption_settings.backgroundColor,
        backgroundOpacity: project.caption_settings.backgroundOpacity,
        animation: project.caption_settings.animation as
          | 'none'
          | 'fade'
          | 'slide-up'
          | 'bounce'
          | 'typewriter'
          | 'color-up'
          | 'bounce-out',
        textAlign: project.caption_settings.textAlign as
          | 'left'
          | 'center'
          | 'right',
        textShadow: project.caption_settings.textShadow,
        borderRadius: project.caption_settings.borderRadius,
        padding: project.caption_settings.padding,
        maxWidth: project.caption_settings.maxWidth,
      }
    : {
        id: 'default-caption-style',
        name: 'Default Caption Style',
        isCustom: false,
        createdAt: new Date().toISOString(),
        fontFamily: 'Inter',
        fontSize: 32,
        fontWeight: 'bold' as const,
        fontStyle: 'normal' as const,
        textColor: '#ffffff',
        highlightColor: '#3b82f6',
        backgroundColor: '#000000',
        backgroundOpacity: 60,
        animation: 'fade' as const,
        textAlign: 'center' as const,
        textShadow: true,
        borderRadius: 8,
        padding: 16,
        maxWidth: 90,
      }

  // Construct the input props object matching the expected format
  return {
    scenes: project.scenes || [],
    subtitlePosition,
    orientation: project.orientation,
    compositionWidth,
    compositionHeight,
    selectedMusic,
    musicVolume: project.music?.volume || 30,
    musicEnabled: project.music?.enabled || false,
    captionStyle,
    exportName,
    exportResolution,
    durationInFrames,
    speech: project.speech,
    duration,
  }
}

export const renderVideo = inngest.createFunction(
  {
    id: 'render-video',
    retries: 1, // Cap retries to only 1 attempt
  },
  { event: 'render-video' },
  async ({ event, step }) => {
    let renderRecord: string | null = null

    try {
      // Step 0: Load project data and reconstruct payload
      const fullPayload = await step.run('load-project-data', async () => {
        const {
          projectId,
          userId,
          organizationId,
          exportResolution,
          duration,
          subtitlePosition,
          compositionHeight,
          compositionWidth,
          durationInFrames,
          exportName,
        } = event.data

        if (projectId) {
          // New format: load project data from database
          console.log('Loading project data from database:', projectId)

          const { data: project, error } = await supabase
            .from('projects')
            .select('*')
            .eq('project_id', projectId)
            .single()

          if (error) {
            throw new Error(`Failed to load project: ${error.message}`)
          }

          if (!project) {
            throw new Error(`Project not found: ${projectId}`)
          }

          console.log('✅ Project data loaded from database')

          // Transform project data to match expected input props format
          const inputProps = transformProjectDataToInputProps(project, {
            subtitlePosition,
            compositionWidth,
            compositionHeight,
            exportName,
            exportResolution,
            durationInFrames,
            duration,
          })

          return {
            inputProps,
            projectId,
            userId,
            organizationId,
          }
        } else {
          // Legacy format: payload is directly in event.data
          console.log('Using legacy payload format')
          return event.data
        }
      })

      const { inputProps, projectId, userId, organizationId } = fullPayload
      // Step 1: Initialize render record
      renderRecord = await step.run('initialize-render', async () => {
        const renderData = {
          project_id: projectId,
          user_id: userId,
          organization_id: organizationId || null,
          status: 'initializing',
          progress: 0,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          export_name: inputProps.exportName || null,
          export_resolution: inputProps.exportResolution || null,
          render_method: null, // will update after method is chosen
        }
        const { data, error } = await supabase
          .from('render_jobs')
          .insert(renderData)
          .select('id')
          .single()

        if (error) {
          console.error('Error creating render job:', error)
          throw error
        }

        return data.id
      })

      // Step 2: Determine render method based on duration
      const renderMethod = await step.run(
        'determine-render-method',
        async () => {
          const estimatedDuration = inputProps.durationInFrames / 30 // Assuming 30fps
          return estimatedDuration > 1800 ? 'cloudrun' : 'lambda' // 30 minutes
        }
      )

      // Update renderMethod in DB
      const { error: methodError } = await supabase
        .from('render_jobs')
        .update({ render_method: renderMethod })
        .eq('id', renderRecord!)

      if (methodError) {
        console.error('Error updating render method:', methodError)
        throw methodError
      }

      // Step 3: Update status to rendering
      await step.run('update-status-rendering', async () => {
        await updateRenderStatus(renderRecord!, 'rendering', 5)
      })

      // Step 4: Render video
      const result = await step.run('render-video', async () => {
        if (!renderRecord) throw new Error('renderRecord is null')
        if (renderMethod === 'lambda') {
          return await renderWithLambda(
            inputProps,
            renderRecord,
            userId,
            projectId
          )
        } else {
          return await renderWithCloudRun(
            inputProps,
            renderRecord,
            userId,
            projectId
          )
        }
      })

      // Step 5: Update final status
      await step.run('finalize-render', async () => {
        await updateRenderStatus(
          renderRecord!,
          'completed',
          100,
          result.publicUrl
        )
      })

      // Step 6: Increment usage for video export
      await step.run('increment-usage', async () => {
        try {
          // Use organization ID if available, otherwise use user ID
          const referenceId = organizationId || userId
          await incrementUsage(referenceId, 'videoExports', 1)
          console.log('✅ Usage incremented for video export')
        } catch (usageError) {
          console.error(
            '❌ Failed to increment usage for video export:',
            usageError
          )
          // Don't fail the entire operation if usage tracking fails
        }
      })

      return {
        renderId: renderRecord,
        publicUrl: result.publicUrl,
        status: 'completed',
      }
    } catch (error) {
      console.error('Render failed:', error)
      if (typeof renderRecord === 'string') {
        await updateRenderStatus(
          renderRecord,
          'failed',
          0,
          undefined,
          (error as Error).message
        )
      }

      throw error
    }
  }
)
