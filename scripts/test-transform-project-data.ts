#!/usr/bin/env bun

/**
 * Test script to verify the transformProjectDataToInputProps function
 * Run with: bun scripts/test-transform-project-data.ts
 */

// Mock project data that matches the actual database response structure
const mockProjectData = {
  project_id: '84cb8d15-4951-4534-a57b-a75bd0753062',
  user_id: 'user_2zrWaX7C6OcFZXGEc4KOBy9joW8',
  organization_id: null,
  project_name: 'Untitled Project',
  method: 'Idea to Video',
  created_at: new Date('2025-07-31T17:15:39.236Z'),
  updated_at: new Date('2025-08-14T11:10:48.067Z'),
  cover_color: '#FFE4E1',
  cover_pic: null,
  orientation: 'landscape',
  duration: '16.115',
  summary: 'The video is split into three scenes, each focusing on a part of making a healthy smoothie at home.',
  voice_regenerations: 2,
  speech: null,
  background_video: null,
  music: {
    src: 'https://storage.googleapis.com/dev_image_adorilabs/v1-stock-music/inspiring-upbeat-corporate-171604.mp3',
    name: 'Upbeat corporate',
    volume: 30,
    enabled: true,
    duration: 115,
  },
  caption_settings: {
    enabled: true,
    padding: 16,
    fontSize: 50,
    maxWidth: 90,
    animation: 'none',
    fontStyle: 'normal',
    textAlign: 'center',
    textColor: '#ffffff',
    fontFamily: 'Inter',
    fontWeight: 'bold',
    textShadow: true,
    borderRadius: 8,
    highlightColor: '#f43f5e',
    backgroundColor: '#3b82f6',
    backgroundOpacity: 25,
  },
  scenes: [
    {
      id: 'scene_1',
      text: 'Ever crave a delicious and healthy smoothie you can whip up in no time? Let\'s get blending!',
      media: {
        fit: 'blur',
        url: 'https://images.pexels.com/photos/8845657/pexels-photo-8845657.jpeg?auto=compress&cs=tinysrgb&h=650&w=940',
        size: {
          width: 6640,
          height: 4427,
        },
        type: 'Image',
        kenBurns: 'zoom-out',
        position: {
          x: 0,
          y: 0,
        },
        thumbnail: 'https://images.pexels.com/photos/8845657/pexels-photo-8845657.jpeg?auto=compress&cs=tinysrgb&h=350',
        transition: {
          type: 'fade',
          duration: 2,
        },
      },
      title: 'Scene scene_1',
      captions: [
        {
          end: 1.498,
          start: 0,
          wordBoundries: [],
        },
        {
          end: 2.577,
          start: 1.498,
          wordBoundries: [],
        },
        {
          end: 3.216,
          start: 2.577,
          wordBoundries: [],
        },
        {
          end: 4.493,
          start: 3.216,
          wordBoundries: [],
        },
        {
          end: 5.155,
          start: 4.493,
          wordBoundries: [],
        },
      ],
      duration: 5.155,
      startOffset: 0,
      voiceSettings: {
        voiceId: '21m00Tcm4TlvDq8ikWAM',
        voiceUrl: 'https://fqoolhppmbqqzxqqtjbn.supabase.co/storage/v1/object/public/assets/user_2zrWaX7C6OcFZXGEc4KOBy9joW8/tts/0eca23ab77e837a3eafa71f51c6b3c6e.mp3',
        voiceVol: 100,
        voiceName: 'Rachel',
        voiceSpeed: 1,
      },
    },
    {
      id: 'scene_2',
      text: 'Start with a handful of fresh spinach, a ripe banana, and a cup of almond milk. Blend until smooth and creamy!',
      media: {
        fit: 'blur',
        url: 'https://images.pexels.com/photos/886521/pexels-photo-886521.jpeg?auto=compress&cs=tinysrgb&h=650&w=940',
        size: {
          width: 1920,
          height: 1080,
        },
        type: 'Image',
        kenBurns: 'none',
        position: {
          x: 0,
          y: 0,
        },
        thumbnail: 'https://images.pexels.com/photos/886521/pexels-photo-886521.jpeg?auto=compress&cs=tinysrgb&h=650&w=940',
        transition: {
          type: 'fade',
          duration: 2,
        },
      },
      title: 'Scene scene_2',
      captions: [
        {
          end: 1.022,
          start: 0,
          wordBoundries: [],
        },
        {
          end: 2.067,
          start: 1.022,
          wordBoundries: [],
        },
        {
          end: 2.995,
          start: 2.067,
          wordBoundries: [],
        },
        {
          end: 4.54,
          start: 2.995,
          wordBoundries: [],
        },
        {
          end: 5.608,
          start: 4.54,
          wordBoundries: [],
        },
        {
          end: 6.223,
          start: 5.608,
          wordBoundries: [],
        },
      ],
      duration: 6.223,
      startOffset: 5.155,
      voiceSettings: {
        voiceId: '21m00Tcm4TlvDq8ikWAM',
        voiceUrl: 'https://fqoolhppmbqqzxqqtjbn.supabase.co/storage/v1/object/public/assets/user_2zrWaX7C6OcFZXGEc4KOBy9joW8/tts/44d56b2fa4b7af80150343138a4ba259.mp3',
        voiceVol: 100,
        voiceName: 'Rachel',
        voiceSpeed: 1,
      },
    },
  ],
  event_id: null,
  run_id: null,
  blog_images: [],
}

// Mock render parameters
const mockRenderParams = {
  subtitlePosition: { x: 234, y: 247 },
  compositionWidth: 854,
  compositionHeight: 480,
  exportName: 'test-video-export',
  exportResolution: '1080p',
  durationInFrames: 483, // 16.115 seconds * 30fps
  duration: 16.115,
}

// Copy the transformation function from render-video.ts (updated version)
function transformProjectDataToInputProps(project: any, params: any) {
  const {
    subtitlePosition,
    compositionWidth,
    compositionHeight,
    exportName,
    exportResolution,
    durationInFrames,
    duration,
  } = params

  // Transform music data to selectedMusic format
  const selectedMusic = project.music
    ? {
        id: 'project-music',
        title: project.music.name || 'Project Music',
        genre: 'Background',
        mood: 'Neutral',
        artistName: 'Unknown',
        provider: 'PROJECT',
        licenseId: 'project',
        previewUrl: project.music.src,
        durationMillis: (project.music.duration || 0) * 1000,
      }
    : null

  // Transform caption settings to captionStyle format
  const captionStyle = project.caption_settings
    ? {
        id: 'project-caption-style',
        name: 'Project Caption Style',
        isCustom: true,
        createdAt: new Date().toISOString(),
        fontFamily: project.caption_settings.fontFamily,
        fontSize: project.caption_settings.fontSize,
        fontWeight: project.caption_settings.fontWeight,
        fontStyle: project.caption_settings.fontStyle,
        textColor: project.caption_settings.textColor,
        highlightColor: project.caption_settings.highlightColor,
        backgroundColor: project.caption_settings.backgroundColor,
        backgroundOpacity: project.caption_settings.backgroundOpacity,
        animation: project.caption_settings.animation,
        textAlign: project.caption_settings.textAlign,
        textShadow: project.caption_settings.textShadow,
        borderRadius: project.caption_settings.borderRadius,
        padding: project.caption_settings.padding,
        maxWidth: project.caption_settings.maxWidth,
      }
    : {
        id: 'default-caption-style',
        name: 'Default Caption Style',
        isCustom: false,
        createdAt: new Date().toISOString(),
        fontFamily: 'Inter',
        fontSize: 32,
        fontWeight: 'bold',
        fontStyle: 'normal',
        textColor: '#ffffff',
        highlightColor: '#3b82f6',
        backgroundColor: '#000000',
        backgroundOpacity: 60,
        animation: 'fade',
        textAlign: 'center',
        textShadow: true,
        borderRadius: 8,
        padding: 16,
        maxWidth: 90,
      }

  // Transform scenes from database format to expected rendering format
  const transformedScenes = (project.scenes || []).map((scene: any) => {
    // Transform captions from database format to expected format
    const transformedCaptions = (scene.captions || []).map((caption: any) => ({
      start: caption.start,
      end: caption.end,
      text: caption.sentence || '', // Database uses 'sentence', rendering expects 'text'
      words: (caption.wordBoundries || []).map((word: any) => ({
        start: word.start,
        end: word.end,
        word: word.word,
      })),
    }))

    // Transform media from database format to expected format
    const transformedMedia = scene.media
      ? {
          id: `media-${scene.id}`,
          type: scene.media.type?.toLowerCase(),
          url: scene.media.url,
          position: scene.media.position || { x: 0, y: 0 },
          size: scene.media.size || { width: 1920, height: 1080 },
          startTime: 0,
          endTime: scene.duration,
          thumbnail: scene.media.thumbnail,
          duration: scene.duration,
          fit: ['blur', 'crop', 'contain'].includes(scene.media.fit || '')
            ? scene.media.fit
            : 'blur',
          transition: scene.media.transition?.type?.toLowerCase() || 'fade',
          kenBurns: [
            'none',
            'zoom-in',
            'zoom-out',
            'pan-left',
            'pan-right',
            'pan-up',
            'pan-down',
          ].includes(scene.media.kenBurns || '')
            ? scene.media.kenBurns
            : 'zoom-in',
          effectDuration: scene.media.transition?.duration || 2,
        }
      : null

    // Create voiceover object from voiceSettings
    const voiceover = scene.voiceSettings?.voiceUrl
      ? {
          audioUrl: scene.voiceSettings.voiceUrl,
          audioDuration: scene.duration,
          volume: (scene.voiceSettings.voiceVol || 100) * 100, // Convert to expected scale
          speed: scene.voiceSettings.voiceSpeed || 1,
        }
      : undefined

    return {
      id: scene.id,
      name: scene.title || `Scene ${scene.id}`,
      duration: scene.duration,
      startOffset: scene.startOffset || 0,
      originalDuration: scene.duration, // Use duration as originalDuration
      text: scene.text,
      voiceSettings: {
        voiceId: scene.voiceSettings?.voiceId || '',
        voiceUrl: scene.voiceSettings?.voiceUrl || '',
        voiceVol: scene.voiceSettings?.voiceVol || 100,
        voiceName: scene.voiceSettings?.voiceName || '',
        voiceSpeed: scene.voiceSettings?.voiceSpeed || 1,
      },
      voiceover,
      captions: transformedCaptions,
      texts: [], // Empty array as expected
      media: transformedMedia,
    }
  })

  // Construct the input props object matching the expected format
  return {
    scenes: transformedScenes,
    subtitlePosition,
    orientation: project.orientation,
    compositionWidth,
    compositionHeight,
    selectedMusic,
    musicVolume: project.music?.volume || 30,
    musicEnabled: project.music?.enabled || false,
    captionStyle,
    exportName,
    exportResolution,
    durationInFrames,
    speech: project.speech,
    duration,
  }
}

console.log('🧪 Testing transformProjectDataToInputProps function with real data structure...\n')

try {
  const result = transformProjectDataToInputProps(mockProjectData, mockRenderParams)
  
  console.log('✅ Transformation successful!')
  console.log('\n📊 Results:')
  console.log('- Scenes count:', result.scenes.length)
  console.log('- Orientation:', result.orientation)
  console.log('- Composition size:', `${result.compositionWidth}x${result.compositionHeight}`)
  console.log('- Music enabled:', result.musicEnabled)
  console.log('- Music volume:', result.musicVolume)
  console.log('- Selected music title:', result.selectedMusic?.title)
  console.log('- Caption style font:', result.captionStyle.fontFamily)
  console.log('- Caption style size:', result.captionStyle.fontSize)
  console.log('- Export name:', result.exportName)
  console.log('- Duration in frames:', result.durationInFrames)
  console.log('- Speech:', result.speech ? 'Present' : 'None')
  
  console.log('\n🔍 Scene structure check:')
  result.scenes.forEach((scene, index) => {
    console.log(`Scene ${index + 1}:`)
    console.log(`  - ID: ${scene.id}`)
    console.log(`  - Name: ${scene.name}`)
    console.log(`  - Duration: ${scene.duration}`)
    console.log(`  - Start offset: ${scene.startOffset}`)
    console.log(`  - Has voiceover: ${scene.voiceover ? 'Yes' : 'No'}`)
    console.log(`  - Voiceover volume: ${scene.voiceover?.volume || 'N/A'}`)
    console.log(`  - Media type: ${scene.media?.type || 'None'}`)
    console.log(`  - Media fit: ${scene.media?.fit || 'N/A'}`)
    console.log(`  - Media kenBurns: ${scene.media?.kenBurns || 'N/A'}`)
    console.log(`  - Captions count: ${scene.captions?.length || 0}`)
    console.log('')
  })
  
  console.log('🎉 Test completed successfully!')
  
} catch (error) {
  console.error('❌ Test failed:', error)
  process.exit(1)
}
