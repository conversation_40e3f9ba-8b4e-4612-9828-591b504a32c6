# Project Data Transformation for Video Rendering

## Overview

This document explains how project data is transformed from the Supabase database format into the input props format expected by the video rendering system in Inngest.

## Problem Statement

- **256KB payload limit** in Inngest production environment
- **Database format mismatch** - Supabase project data structure differs from rendering system expectations
- **Need for transformation** - Convert raw project data into the exact format expected by the video rendering pipeline

## Solution

### 1. Database Retrieval

Instead of passing large payloads through Inngest, we now:

- Pass only essential metadata (projectId, userId, render parameters)
- Fetch full project data directly from Supabase within the Inngest function
- Transform the data to match expected input props format

### 2. Transformation Function

The `transformProjectDataToInputProps` function in `src/inngest/functions/render-video.ts` handles the conversion:

```typescript
function transformProjectDataToInputProps(
  project: ProjectData,
  params: RenderParams
)
```

### 3. Key Transformations

#### Music Data

**Database format:**

```json
{
  "enabled": true,
  "src": "https://example.com/music.mp3",
  "volume": 45,
  "duration": 120,
  "name": "Background Track"
}
```

**Transformed to `selectedMusic`:**

```json
{
  "id": "project-music",
  "title": "Background Track",
  "genre": "Background",
  "mood": "Neutral",
  "artistName": "Unknown",
  "provider": "PROJECT",
  "licenseId": "project",
  "previewUrl": "https://example.com/music.mp3",
  "durationMillis": 120000
}
```

#### Caption Settings

**Database format:**

```json
{
  "enabled": true,
  "fontFamily": "Nunito",
  "fontSize": 50,
  "fontWeight": "bold"
  // ... other properties
}
```

**Transformed to `captionStyle`:**

```json
{
  "id": "project-caption-style",
  "name": "Project Caption Style",
  "isCustom": true,
  "createdAt": "2025-01-14T...",
  "fontFamily": "Nunito",
  "fontSize": 50
  // ... other properties with proper typing
}
```

#### Scene Data Transformation

**Database scene format:**

```json
{
  "id": "scene_1",
  "text": "Scene text content",
  "title": "Scene scene_1",
  "duration": 5.155,
  "startOffset": 0,
  "voiceSettings": {
    "voiceId": "21m00Tcm4TlvDq8ikWAM",
    "voiceUrl": "https://example.com/voice.mp3",
    "voiceVol": 100,
    "voiceName": "Rachel",
    "voiceSpeed": 1
  },
  "captions": [
    {
      "start": 0,
      "end": 1.498,
      "wordBoundries": []
    }
  ],
  "media": {
    "type": "Image",
    "url": "https://example.com/image.jpg",
    "fit": "blur",
    "kenBurns": "zoom-out",
    "transition": {
      "type": "fade",
      "duration": 2
    }
  }
}
```

**Transformed scene format:**

```json
{
  "id": "scene_1",
  "name": "Scene scene_1",
  "duration": 5.155,
  "startOffset": 0,
  "originalDuration": 5.155,
  "text": "Scene text content",
  "voiceSettings": {
    "voiceId": "21m00Tcm4TlvDq8ikWAM",
    "voiceUrl": "https://example.com/voice.mp3",
    "voiceVol": 100,
    "voiceName": "Rachel",
    "voiceSpeed": 1
  },
  "voiceover": {
    "audioUrl": "https://example.com/voice.mp3",
    "audioDuration": 5.155,
    "volume": 10000,
    "speed": 1
  },
  "captions": [
    {
      "start": 0,
      "end": 1.498,
      "text": "",
      "words": []
    }
  ],
  "texts": [],
  "media": {
    "id": "media-scene_1",
    "type": "image",
    "url": "https://example.com/image.jpg",
    "position": { "x": 0, "y": 0 },
    "size": { "width": 1920, "height": 1080 },
    "startTime": 0,
    "endTime": 5.155,
    "duration": 5.155,
    "fit": "blur",
    "transition": "fade",
    "kenBurns": "zoom-out",
    "effectDuration": 2
  }
}
```

### 4. Default Handling

When project data is missing optional fields:

- **No music**: `selectedMusic` becomes `null`, `musicEnabled` becomes `false`, `musicVolume` defaults to `30`
- **No caption settings**: Uses default Inter font with standard styling
- **No scenes**: Returns empty array
- **No speech**: Remains `null` for regular video projects
- **Missing voiceSettings**: Creates empty voiceSettings object with default values
- **Missing media**: Sets media to `null`
- **Missing captions**: Creates empty captions array

### 5. Scene-Specific Transformations

The transformation handles several key scene-level conversions:

1. **Voiceover Creation**: Generates `voiceover` object from `voiceSettings` with proper volume scaling (voiceVol \* 100)
2. **Media Transformation**: Converts database media format to rendering format with additional fields like `id`, `startTime`, `endTime`, `effectDuration`
3. **Caption Conversion**: Transforms `wordBoundries` to `words` and adds `text` field
4. **Type Safety**: Ensures proper typing for `fit`, `transition`, and `kenBurns` properties

### 6. Output Format

The transformation produces an object matching the expected input props structure:

```typescript
{
  scenes: Array<Record<string, unknown>>,
  subtitlePosition: { x: number, y: number },
  orientation: string,
  compositionWidth: number,
  compositionHeight: number,
  selectedMusic: SelectedMusic | null,
  musicVolume: number,
  musicEnabled: boolean,
  captionStyle: CaptionStyle,
  exportName: string,
  exportResolution: string,
  durationInFrames: number,
  speech: Speech | null,
  duration: number
}
```

## 7. Testing

A test script is available at `scripts/test-transform-project-data.ts` that verifies:

- ✅ Successful transformation of complete project data with real database structure
- ✅ Proper handling of speech/podcast projects
- ✅ Correct defaults when music/caption settings are null
- ✅ All required fields are present in output
- ✅ Proper structure for `selectedMusic` and `captionStyle`
- ✅ Scene transformation including voiceover, media, and captions
- ✅ Type safety and proper field mapping

Run the test with:

```bash
bun scripts/test-transform-project-data.ts
```

## 8. Benefits

1. **Payload Size Reduction**: Eliminates 256KB limit issues by fetching data server-side
2. **Data Consistency**: Ensures project data is always current from database
3. **Format Compatibility**: Maintains compatibility with existing rendering pipeline
4. **Error Handling**: Provides proper defaults for missing data
5. **Type Safety**: Maintains TypeScript type safety throughout transformation

## 9. Usage in Inngest

The transformation is automatically applied in the `render-video` Inngest function:

```typescript
const inputProps = transformProjectDataToInputProps(project, {
  subtitlePosition,
  compositionWidth,
  compositionHeight,
  exportName,
  exportResolution,
  durationInFrames,
  duration,
})
```

This ensures that the rendering system receives data in the exact format it expects, regardless of how the data is stored in the database.
