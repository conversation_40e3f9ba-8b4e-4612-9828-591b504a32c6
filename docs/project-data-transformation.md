# Project Data Transformation for Video Rendering

## Overview

This document explains how project data is transformed from the Supabase database format into the input props format expected by the video rendering system in Inngest.

## Problem Statement

- **256KB payload limit** in Inngest production environment
- **Database format mismatch** - Supabase project data structure differs from rendering system expectations
- **Need for transformation** - Convert raw project data into the exact format expected by the video rendering pipeline

## Solution

### 1. Database Retrieval
Instead of passing large payloads through Inngest, we now:
- Pass only essential metadata (projectId, userId, render parameters)
- Fetch full project data directly from Supabase within the Inngest function
- Transform the data to match expected input props format

### 2. Transformation Function

The `transformProjectDataToInputProps` function in `src/inngest/functions/render-video.ts` handles the conversion:

```typescript
function transformProjectDataToInputProps(
  project: ProjectData,
  params: RenderParams
)
```

### 3. Key Transformations

#### Music Data
**Database format:**
```json
{
  "enabled": true,
  "src": "https://example.com/music.mp3",
  "volume": 45,
  "duration": 120,
  "name": "Background Track"
}
```

**Transformed to `selectedMusic`:**
```json
{
  "id": "project-music",
  "title": "Background Track",
  "genre": "Background",
  "mood": "Neutral",
  "artistName": "Unknown",
  "provider": "PROJECT",
  "licenseId": "project",
  "previewUrl": "https://example.com/music.mp3",
  "durationMillis": 120000
}
```

#### Caption Settings
**Database format:**
```json
{
  "enabled": true,
  "fontFamily": "Nunito",
  "fontSize": 50,
  "fontWeight": "bold",
  // ... other properties
}
```

**Transformed to `captionStyle`:**
```json
{
  "id": "project-caption-style",
  "name": "Project Caption Style",
  "isCustom": true,
  "createdAt": "2025-01-14T...",
  "fontFamily": "Nunito",
  "fontSize": 50,
  // ... other properties with proper typing
}
```

### 4. Default Handling

When project data is missing optional fields:

- **No music**: `selectedMusic` becomes `null`, `musicEnabled` becomes `false`, `musicVolume` defaults to `30`
- **No caption settings**: Uses default Inter font with standard styling
- **No scenes**: Returns empty array
- **No speech**: Remains `null` for regular video projects

### 5. Output Format

The transformation produces an object matching the expected input props structure:

```typescript
{
  scenes: Array<Record<string, unknown>>,
  subtitlePosition: { x: number, y: number },
  orientation: string,
  compositionWidth: number,
  compositionHeight: number,
  selectedMusic: SelectedMusic | null,
  musicVolume: number,
  musicEnabled: boolean,
  captionStyle: CaptionStyle,
  exportName: string,
  exportResolution: string,
  durationInFrames: number,
  speech: Speech | null,
  duration: number
}
```

## Testing

A test script is available at `scripts/test-transform-project-data.ts` that verifies:
- ✅ Successful transformation of complete project data
- ✅ Proper handling of speech/podcast projects
- ✅ Correct defaults when music/caption settings are null
- ✅ All required fields are present in output
- ✅ Proper structure for `selectedMusic` and `captionStyle`

Run the test with:
```bash
bun scripts/test-transform-project-data.ts
```

## Benefits

1. **Payload Size Reduction**: Eliminates 256KB limit issues by fetching data server-side
2. **Data Consistency**: Ensures project data is always current from database
3. **Format Compatibility**: Maintains compatibility with existing rendering pipeline
4. **Error Handling**: Provides proper defaults for missing data
5. **Type Safety**: Maintains TypeScript type safety throughout transformation

## Usage in Inngest

The transformation is automatically applied in the `render-video` Inngest function:

```typescript
const inputProps = transformProjectDataToInputProps(project, {
  subtitlePosition,
  compositionWidth,
  compositionHeight,
  exportName,
  exportResolution,
  durationInFrames,
  duration,
})
```

This ensures that the rendering system receives data in the exact format it expects, regardless of how the data is stored in the database.
