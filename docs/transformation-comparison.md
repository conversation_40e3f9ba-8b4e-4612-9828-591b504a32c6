# Project Data Transformation Comparison

## Database Structure vs Expected Rendering Format

### Database Project Structure (Input)
```json
{
  "projectId": "84cb8d15-4951-4534-a57b-a75bd0753062",
  "userId": "user_2zrWaX7C6OcFZXGEc4KOBy9joW8",
  "projectName": "Untitled Project",
  "orientation": "landscape",
  "duration": 16.115,
  "music": {
    "src": "https://storage.googleapis.com/dev_image_adorilabs/v1-stock-music/inspiring-upbeat-corporate-171604.mp3",
    "name": "Upbeat corporate",
    "volume": 30,
    "enabled": true,
    "duration": 115
  },
  "captionSettings": {
    "enabled": true,
    "fontFamily": "Inter",
    "fontSize": 50,
    "animation": "none",
    "textColor": "#ffffff",
    "highlightColor": "#f43f5e",
    "backgroundColor": "#3b82f6",
    "backgroundOpacity": 25
  },
  "scenes": [
    {
      "id": "scene_1",
      "text": "Ever crave a delicious and healthy smoothie...",
      "title": "Scene scene_1",
      "duration": 5.155,
      "startOffset": 0,
      "voiceSettings": {
        "voiceId": "21m00Tcm4TlvDq8ikWAM",
        "voiceUrl": "https://example.com/voice.mp3",
        "voiceVol": 100,
        "voiceName": "Rachel",
        "voiceSpeed": 1
      },
      "captions": [
        {
          "start": 0,
          "end": 1.498,
          "wordBoundries": []
        }
      ],
      "media": {
        "type": "Image",
        "url": "https://example.com/image.jpg",
        "fit": "blur",
        "kenBurns": "zoom-out",
        "transition": {
          "type": "fade",
          "duration": 2
        }
      }
    }
  ]
}
```

### Expected Rendering Format (Output)
```json
{
  "scenes": [
    {
      "id": "scene_1",
      "name": "Scene scene_1",
      "duration": 5.155,
      "startOffset": 0,
      "originalDuration": 5.155,
      "text": "Ever crave a delicious and healthy smoothie...",
      "voiceSettings": {
        "voiceId": "21m00Tcm4TlvDq8ikWAM",
        "voiceUrl": "https://example.com/voice.mp3",
        "voiceVol": 100,
        "voiceName": "Rachel",
        "voiceSpeed": 1
      },
      "voiceover": {
        "audioUrl": "https://example.com/voice.mp3",
        "audioDuration": 5.155,
        "volume": 10000,
        "speed": 1
      },
      "captions": [
        {
          "start": 0,
          "end": 1.498,
          "words": []
        }
      ],
      "texts": [],
      "media": {
        "id": "media-scene_1",
        "type": "image",
        "url": "https://example.com/image.jpg",
        "position": { "x": 0, "y": 0 },
        "size": { "width": 1920, "height": 1080 },
        "startTime": 0,
        "endTime": 5.155,
        "duration": 5.155,
        "fit": "blur",
        "transition": "fade",
        "kenBurns": "zoom-out",
        "effectDuration": 2
      }
    }
  ],
  "orientation": "landscape",
  "selectedMusic": {
    "id": "api-music",
    "title": "Upbeat corporate",
    "genre": "Background",
    "mood": "Neutral",
    "artistName": "Unknown",
    "provider": "API",
    "licenseId": "api",
    "previewUrl": "https://storage.googleapis.com/dev_image_adorilabs/v1-stock-music/inspiring-upbeat-corporate-171604.mp3",
    "durationMillis": 115000
  },
  "musicVolume": 30,
  "musicEnabled": true,
  "captionStyle": {
    "id": "custom-preview",
    "name": "Live Preview",
    "isCustom": true,
    "fontFamily": "Inter",
    "fontSize": 50,
    "animation": "none",
    "textColor": "#ffffff",
    "highlightColor": "#f43f5e",
    "backgroundColor": "#3b82f6",
    "backgroundOpacity": 25
  }
}
```

## Key Transformations

### 1. Music → selectedMusic
- `music.name` → `selectedMusic.title`
- `music.src` → `selectedMusic.previewUrl`
- `music.duration` → `selectedMusic.durationMillis` (seconds to milliseconds)
- Added required fields: `id`, `genre`, `mood`, `artistName`, `provider`, `licenseId`

### 2. captionSettings → captionStyle
- Added metadata: `id`, `name`, `isCustom`, `createdAt`
- Preserved all styling properties

### 3. Scene Transformations
- `scene.title` → `scene.name`
- Added `originalDuration` field
- Created `voiceover` object from `voiceSettings`
- Transformed `captions.wordBoundries` → `captions.words`
- Enhanced media object with additional fields

### 4. Media Object Enhancement
- Added `id` field (`media-${scene.id}`)
- Added timing fields: `startTime`, `endTime`, `duration`
- Added `effectDuration` from transition
- Type conversion: `Image` → `image`
- Added default `position` and `size` if missing

### 5. Volume Scaling
- `voiceSettings.voiceVol` (0-100) → `voiceover.volume` (0-10000)
- Scaling factor: `voiceVol * 100`

## Result
The transformation successfully converts the database project structure into the exact format expected by the video rendering system, ensuring compatibility while solving the 256KB payload limit issue.
